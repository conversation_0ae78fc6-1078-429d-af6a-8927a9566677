using SmaTrendFollower.Models;
using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Monitoring;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced signal generator with RSI and MACD technical indicators.
/// Implements SMA momentum strategy with additional technical analysis.
/// </summary>
public sealed class SignalGenerator : ISignalGenerator
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly IMomentumCache? _momentumCache;
    private readonly ILogger<SignalGenerator> _logger;
    private readonly AnomalyDetectorService? _anomalyDetector;

    // Technical indicator periods
    private const int RsiPeriod = 14;
    private const int MacdFastPeriod = 12;
    private const int MacdSlowPeriod = 26;
    private const int MacdSignalPeriod = 9;

    public SignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILogger<SignalGenerator> logger,
        IMomentumCache? momentumCache = null,
        AnomalyDetectorService? anomalyDetector = null)
    {
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _universeProvider = universeProvider ?? throw new ArgumentNullException(nameof(universeProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _momentumCache = momentumCache;
        _anomalyDetector = anomalyDetector;
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10, CancellationToken cancellationToken = default)
    {
        var sw = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting enhanced signal generation with RSI/MACD for top {TopN} signals", topN);

            // Get universe of symbols
            var symbols = await _universeProvider.GetSymbolsAsync();
            var symbolList = symbols.ToList();

            if (symbolList.Count == 0)
            {
                _logger.LogWarning("No symbols available from universe provider");
                MetricsRegistry.SignalsTotal.WithLabels("enhanced", "false").Inc();
                return Enumerable.Empty<TradingSignal>();
            }

            // Update universe size metric
            MetricsRegistry.CurrentUniverseSize.Set(symbolList.Count);

            _logger.LogInformation("Screening {Count} symbols for trading signals", symbolList.Count);

            var signals = new List<TradingSignal>();

            // Process each symbol
            foreach (var symbol in symbolList)
            {
                try
                {
                    // Check if trading is halted for this symbol
                    if (_anomalyDetector != null && await _anomalyDetector.IsTradingHaltedAsync(symbol))
                    {
                        _logger.LogDebug("Skipping signal generation for {Symbol} - trading halted due to anomaly", symbol);
                        continue;
                    }

                    var signal = await ProcessSymbol(symbol, cancellationToken);
                    if (signal != null)
                    {
                        signals.Add(signal.Value);
                        // Record signal generated
                        MetricsRegistry.SignalsTotal.WithLabels("enhanced", "true").Inc();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process symbol {Symbol}", symbol);
                    MetricsRegistry.ApplicationErrors.WithLabels("SignalGenerator", "ProcessSymbol").Inc();
                }
            }

            // Filter and rank signals
            var validSignals = signals
                .Where(s => s.Price > 0 && s.Atr > 0)
                .Where(s => s.Atr / s.Price < 0.03m) // ATR/Price < 3% (volatility throttle)
                .Where(s => !string.IsNullOrEmpty(s.Symbol))
                .OrderByDescending(s => s.SixMonthReturn)
                .Take(topN)
                .ToList();

            _logger.LogInformation("Generated {Count} enhanced trading signals from {Total} symbols",
                validSignals.Count, symbolList.Count);

            // Record signal generation latency
            MetricsRegistry.SignalLatencyMs.Observe(sw.Elapsed.TotalMilliseconds);

            return validSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in enhanced signal generation");
            MetricsRegistry.ApplicationErrors.WithLabels("SignalGenerator", "RunAsync").Inc();
            return Enumerable.Empty<TradingSignal>();
        }
        finally
        {
            sw.Stop();
        }
    }

    private async Task<TradingSignal?> ProcessSymbol(string symbol, CancellationToken cancellationToken = default)
    {
        // Get historical bars (need at least 200 days for SMA200 calculation)
        var endDate = DateTime.UtcNow.Date;
        var startDate = endDate.AddDays(-250); // Get 250 days of data to ensure we have enough
        var barsPage = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
        var barsList = barsPage.Items.ToList();

        if (barsList.Count < 200) // Need at least 200 bars for SMA200 calculation
        {
            _logger.LogDebug("Insufficient bars for {Symbol}: {Count}", symbol, barsList.Count);
            return null;
        }

        // Calculate basic technical indicators
        var currentPrice = barsList.Last().Close;
        var sma50 = (decimal)barsList.GetSma50();
        var sma200 = (decimal)barsList.GetSma200();
        var atr14 = (decimal)barsList.GetAtr14();
        var sixMonthReturn = (decimal)barsList.GetTotalReturn(126); // ~6 months

        // Validate calculated values
        if (currentPrice <= 0 || sma50 <= 0 || sma200 <= 0 || atr14 <= 0)
        {
            _logger.LogDebug("Invalid calculated values for {Symbol}", symbol);
            return null;
        }

        // Calculate RSI and MACD
        double rsi;
        Models.MacdResult macd;

        try
        {
            // Try to get from cache first
            var cachedRsi = _momentumCache != null ? await _momentumCache.GetRsiAsync(symbol) : null;
            var cachedMacd = _momentumCache != null ? await _momentumCache.GetMacdAsync(symbol) : null;

            if (cachedRsi.HasValue && cachedMacd.HasValue)
            {
                rsi = cachedRsi.Value;
                macd = cachedMacd.Value;
                _logger.LogDebug("Using cached RSI/MACD for {Symbol}", symbol);
            }
            else
            {
                // --- Performance patch --------------------------------------------------
                // Heavy CPU calculations offloaded to Task.Run so caller's thread isn't
                // blocked; cancellation token propagated for proper async behavior.
                (rsi, macd) = await Task.Run(() =>
                {
                    double r = barsList.GetRsi14();
                    Models.MacdResult m = barsList.GetMacd();
                    return (r, m);
                }, cancellationToken).ConfigureAwait(false);

                // Cache the results
                if (_momentumCache != null)
                {
                    await _momentumCache.SetRsiAsync(symbol, rsi);
                    await _momentumCache.SetMacdAsync(symbol, macd);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to calculate RSI/MACD for {Symbol}", symbol);
            return null;
        }

        // Enhanced signal logic with RSI and MACD
        bool longSetup = EvaluateLongSetup(currentPrice, sma50, sma200, rsi, macd);
        bool shortSetup = EvaluateShortSetup(currentPrice, sma50, sma200, rsi, macd);

        // For now, only generate long signals (consistent with existing strategy)
        if (longSetup)
        {
            var momentum = CalculateMomentumScore(rsi, macd);
            return new TradingSignal(symbol, currentPrice, atr14, sixMonthReturn, momentum);
        }

        return null; // Signal doesn't meet criteria
    }

    private static bool EvaluateLongSetup(decimal currentPrice, decimal sma50, decimal sma200, double rsi, Models.MacdResult macd)
    {
        // Basic trend filter: close > sma50 && close > sma200 (uptrend filter)
        bool trendFilter = currentPrice > sma50 && currentPrice > sma200;

        // RSI filter: RSI > 55 (momentum confirmation, avoid oversold)
        bool rsiFilter = rsi > 55;

        // MACD filter: MACD histogram > 0 (bullish momentum)
        bool macdFilter = macd.Histogram > 0;

        return trendFilter && rsiFilter && macdFilter;
    }

    private static bool EvaluateShortSetup(decimal currentPrice, decimal sma50, decimal sma200, double rsi, Models.MacdResult macd)
    {
        // Basic trend filter: close < sma50 && close < sma200 (downtrend filter)
        bool trendFilter = currentPrice < sma50 && currentPrice < sma200;

        // RSI filter: RSI < 45 (bearish momentum, avoid overbought)
        bool rsiFilter = rsi < 45;

        // MACD filter: MACD histogram < 0 (bearish momentum)
        bool macdFilter = macd.Histogram < 0;

        return trendFilter && rsiFilter && macdFilter;
    }

    private static decimal CalculateMomentumScore(double rsi, Models.MacdResult macd)
    {
        // Normalize RSI to 0-1 scale (50 = neutral)
        var rsiScore = (decimal)((rsi - 50) / 50);

        // Normalize MACD histogram (use absolute value and cap at reasonable level)
        var macdScore = (decimal)Math.Sign(macd.Histogram) * Math.Min(Math.Abs((decimal)macd.Histogram), 1.0m);

        // Combine scores with equal weighting
        return (rsiScore + macdScore) / 2;
    }
}
