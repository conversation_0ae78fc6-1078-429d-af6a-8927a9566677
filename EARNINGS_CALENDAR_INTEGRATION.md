# Earnings Calendar Integration

## Overview

The SmaTrendFollower system now includes earnings calendar integration to avoid trading near earnings announcements. This feature helps reduce volatility risk by filtering out symbols with upcoming earnings within a configurable timeframe.

## Implementation

### Core Components

1. **IEarningsCalendar Interface**
   - `GetNextEarningsAsync(string symbol, CancellationToken ct)` - Returns the next earnings date for a symbol

2. **PolygonEarningsCalendar Service**
   - Polygon.io API integration for earnings data
   - Memory caching with 6-hour TTL for performance
   - Robust error handling and timeout management
   - Automatic retry and fallback mechanisms

3. **WheelStrategyEngine Integration**
   - Automatic earnings filtering in the evaluation cycle
   - Configurable 7-day exclusion window before earnings
   - Detailed logging for transparency

### Service Registration

The earnings calendar is automatically registered in the wheel strategy services:

```csharp
// In ServiceConfiguration.AddWheelStrategyServices()
services.AddHttpClient<IEarningsCalendar, PolygonEarningsCalendar>()
        .SetHandlerLifetime(TimeSpan.FromMinutes(30));
services.AddMemoryCache();
```

### Configuration

Set your Polygon API key in environment variables:
```bash
POLY_API_KEY=your_polygon_api_key_here
```

Or in appsettings.json:
```json
{
  "Polygon": {
    "ApiKey": "your_polygon_api_key_here"
  }
}
```

## Usage

### Wheel Strategy Integration

The earnings calendar is automatically integrated into the wheel strategy engine. When evaluating new opportunities, the system will:

1. Check for upcoming earnings announcements
2. Skip symbols with earnings within 7 days
3. Log the decision for transparency

Example log output:
```
🎡 Skipping AAPL - earnings in 3.2 days on 2024-01-25
```

### Manual Testing

Test the earnings calendar functionality:

```bash
dotnet run --project SmaTrendFollower.Console test-earnings
```

This will:
- Test earnings data retrieval for major symbols
- Verify service registration and integration
- Display upcoming earnings dates and timing

### Programmatic Usage

```csharp
// Inject the service
private readonly IEarningsCalendar _earningsCalendar;

// Check for upcoming earnings
var nextEarnings = await _earningsCalendar.GetNextEarningsAsync("AAPL");
if (nextEarnings.HasValue)
{
    var daysToEarnings = (nextEarnings.Value - DateTime.UtcNow).TotalDays;
    if (daysToEarnings < 7)
    {
        // Skip trading this symbol
        return;
    }
}
```

## Features

### Caching Strategy
- **Memory Cache**: 6-hour TTL for successful results
- **Null Cache**: 1-hour TTL for symbols with no earnings data
- **Thread-Safe**: Concurrent access supported

### Error Handling
- **HTTP Timeouts**: 30-second timeout with graceful degradation
- **API Errors**: Proper handling of 404, 500, and other HTTP errors
- **JSON Parsing**: Robust parsing with error recovery
- **Cancellation**: Full cancellation token support

### Performance
- **Async/Await**: Non-blocking operations throughout
- **Connection Pooling**: Efficient HTTP client management
- **Rate Limiting**: Respects Polygon API rate limits

## API Integration

### Polygon Earnings Endpoint

The service uses the Polygon.io earnings reference endpoint:
```
GET /v3/reference/earnings/{symbol}?apiKey={key}
```

### Response Format

Expected JSON response:
```json
{
  "results": [
    {
      "reportTime": "2024-01-25T16:30:00Z",
      "fiscalPeriod": "Q4",
      "fiscalYear": "2023"
    }
  ]
}
```

### Data Processing

- **Future Dates Only**: Only considers earnings dates in the future
- **UTC Conversion**: Handles timezone conversion automatically
- **Multiple Results**: Finds the next upcoming earnings from multiple results

## Risk Management

### Benefits
- **Volatility Reduction**: Avoids high-volatility periods around earnings
- **Improved Consistency**: More predictable trading outcomes
- **Risk Control**: Reduces exposure to earnings surprises

### Configuration
- **Exclusion Window**: 7 days before earnings (configurable)
- **Fallback Behavior**: Graceful degradation when earnings data unavailable
- **Override Capability**: Can be disabled for testing or special circumstances

## Monitoring

### Logging
- **Debug Level**: API requests and cache hits/misses
- **Info Level**: Earnings dates and trading decisions
- **Warning Level**: API errors and timeouts
- **Error Level**: Unexpected failures

### Metrics
- Cache hit/miss ratios
- API response times
- Error rates by symbol
- Earnings exclusion counts

## Testing

### Unit Tests
- Mock HTTP responses for various scenarios
- Cache behavior verification
- Error handling validation
- Edge case coverage

### Integration Tests
- Live API connectivity
- Service registration verification
- End-to-end workflow testing

### Performance Tests
- Concurrent request handling
- Cache efficiency measurement
- Memory usage monitoring

## Future Enhancements

### Planned Features
1. **Multiple Data Sources**: Yahoo Finance, Alpha Vantage fallbacks
2. **Advanced Filtering**: Exclude by earnings importance/impact
3. **Historical Analysis**: Track earnings impact on trading performance
4. **Custom Windows**: Symbol-specific exclusion periods
5. **Earnings Alerts**: Discord notifications for upcoming earnings

### Configuration Options
- Configurable exclusion window (currently 7 days)
- Multiple API key rotation
- Custom cache TTL settings
- Earnings impact scoring

## Troubleshooting

### Common Issues

1. **404 Errors**: Check API key permissions and symbol format
2. **Timeout Errors**: Verify network connectivity and API status
3. **Cache Issues**: Clear memory cache or restart application
4. **Missing Data**: Some symbols may not have earnings data available

### Debug Commands

```bash
# Test specific symbol
dotnet run --project SmaTrendFollower.Console test-earnings

# Check service registration
dotnet run --project SmaTrendFollower.Console health

# Monitor API calls
# Enable debug logging in appsettings.json
```

## Dependencies

- **Polygon.io API**: Earnings data source
- **Microsoft.Extensions.Caching.Memory**: In-memory caching
- **Microsoft.Extensions.Http**: HTTP client factory
- **System.Text.Json**: JSON parsing
- **Polly**: Resilience and retry policies (via existing infrastructure)
