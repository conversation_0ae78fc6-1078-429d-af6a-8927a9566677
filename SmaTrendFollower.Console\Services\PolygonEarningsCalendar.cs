using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Polygon.io implementation of earnings calendar service
/// </summary>
public sealed class PolygonEarningsCalendar : IEarningsCalendar
{
    private readonly HttpClient _http;
    private readonly string _apiKey;
    private readonly IMemoryCache _cache;
    private readonly ILogger<PolygonEarningsCalendar> _logger;
    
    private static readonly TimeSpan CacheDuration = TimeSpan.FromHours(6);
    
    public PolygonEarningsCalendar(
        HttpClient http, 
        IConfiguration configuration,
        IMemoryCache cache,
        ILogger<PolygonEarningsCalendar> logger)
    {
        _http = http;
        _cache = cache;
        _logger = logger;
        
        _apiKey = Environment.GetEnvironmentVariable("POLY_API_KEY") 
                  ?? configuration["Polygon:ApiKey"] 
                  ?? throw new InvalidOperationException("POLY_API_KEY environment variable or Polygon:ApiKey configuration is required");
        
        _http.BaseAddress = new Uri("https://api.polygon.io/");
        _http.Timeout = TimeSpan.FromSeconds(30);
    }

    public async Task<DateTime?> GetNextEarningsAsync(string symbol, CancellationToken ct = default)
    {
        if (string.IsNullOrWhiteSpace(symbol))
        {
            _logger.LogWarning("Empty symbol provided to GetNextEarningsAsync");
            return null;
        }

        var cacheKey = $"earnings:{symbol.ToUpperInvariant()}";
        
        // Check cache first
        if (_cache.TryGetValue(cacheKey, out DateTime? cachedDate))
        {
            _logger.LogDebug("Returning cached earnings date for {Symbol}: {Date}", symbol, cachedDate);
            return cachedDate;
        }

        try
        {
            var url = $"v3/reference/earnings/{symbol}?apiKey={_apiKey}";
            _logger.LogDebug("Fetching earnings data for {Symbol} from Polygon", symbol);
            
            var response = await _http.GetAsync(url, ct).ConfigureAwait(false);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Polygon earnings API returned {StatusCode} for {Symbol}", 
                    response.StatusCode, symbol);
                return null;
            }

            var content = await response.Content.ReadAsStringAsync(ct).ConfigureAwait(false);
            var root = JsonSerializer.Deserialize<JsonElement>(content);
            
            if (!root.TryGetProperty("results", out var resultsArray) || 
                resultsArray.GetArrayLength() == 0)
            {
                _logger.LogDebug("No earnings data found for {Symbol}", symbol);
                
                // Cache null result for shorter duration to avoid repeated API calls
                _cache.Set(cacheKey, (DateTime?)null, TimeSpan.FromHours(1));
                return null;
            }

            // Find the next earnings date (future dates only)
            var now = DateTime.UtcNow;
            DateTime? nextEarnings = null;
            
            foreach (var result in resultsArray.EnumerateArray())
            {
                if (result.TryGetProperty("reportTime", out var reportTimeElement))
                {
                    var reportTimeStr = reportTimeElement.GetString();
                    if (!string.IsNullOrEmpty(reportTimeStr) && 
                        DateTime.TryParse(reportTimeStr, out var reportTime))
                    {
                        // Convert to UTC if needed
                        if (reportTime.Kind == DateTimeKind.Unspecified)
                        {
                            reportTime = DateTime.SpecifyKind(reportTime, DateTimeKind.Utc);
                        }
                        else if (reportTime.Kind == DateTimeKind.Local)
                        {
                            reportTime = reportTime.ToUniversalTime();
                        }
                        
                        // Only consider future earnings
                        if (reportTime > now && (nextEarnings == null || reportTime < nextEarnings))
                        {
                            nextEarnings = reportTime;
                        }
                    }
                }
            }
            
            _logger.LogDebug("Next earnings for {Symbol}: {Date}", symbol, nextEarnings);
            
            // Cache the result
            _cache.Set(cacheKey, nextEarnings, CacheDuration);
            
            return nextEarnings;
        }
        catch (TaskCanceledException)
        {
            _logger.LogWarning("Timeout fetching earnings data for {Symbol}", symbol);
            return null;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error fetching earnings data for {Symbol}", symbol);
            return null;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON parsing error for earnings data for {Symbol}", symbol);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error fetching earnings data for {Symbol}", symbol);
            return null;
        }
    }
}
