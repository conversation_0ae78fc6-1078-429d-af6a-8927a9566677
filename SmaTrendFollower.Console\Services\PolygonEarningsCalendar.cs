using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Polygon.io implementation of earnings calendar service
/// </summary>
public sealed class PolygonEarningsCalendar : IEarningsCalendar
{
    private readonly HttpClient _http;
    private readonly string _apiKey;
    private readonly IMemoryCache _cache;
    private readonly ILogger<PolygonEarningsCalendar> _logger;
    
    private static readonly TimeSpan CacheDuration = TimeSpan.FromHours(6);
    
    public PolygonEarningsCalendar(
        HttpClient http, 
        IConfiguration configuration,
        IMemoryCache cache,
        ILogger<PolygonEarningsCalendar> logger)
    {
        _http = http;
        _cache = cache;
        _logger = logger;
        
        _apiKey = Environment.GetEnvironmentVariable("POLY_API_KEY") 
                  ?? configuration["Polygon:ApiKey"] 
                  ?? throw new InvalidOperationException("POLY_API_KEY environment variable or Polygon:ApiKey configuration is required");
        
        _http.BaseAddress = new Uri("https://api.polygon.io/");
        _http.Timeout = TimeSpan.FromSeconds(30);
    }

    public async Task<DateTime?> GetNextEarningsAsync(string symbol, CancellationToken ct = default)
    {
        if (string.IsNullOrWhiteSpace(symbol))
        {
            _logger.LogWarning("Empty symbol provided to GetNextEarningsAsync");
            return null;
        }

        var cacheKey = $"earnings:{symbol.ToUpperInvariant()}";

        // Check cache first
        if (_cache.TryGetValue(cacheKey, out DateTime? cachedDate))
        {
            _logger.LogDebug("Returning cached earnings date for {Symbol}: {Date}", symbol, cachedDate);
            return cachedDate;
        }

        // Try multiple endpoints in order of preference
        var endpoints = new[]
        {
            $"v3/reference/earnings/{symbol}?apiKey={_apiKey}",
            $"v2/reference/earnings/{symbol}?apiKey={_apiKey}",
            $"v1/reference/earnings/{symbol}?apiKey={_apiKey}"
        };

        foreach (var endpoint in endpoints)
        {
            try
            {
                _logger.LogDebug("Trying earnings endpoint: {Endpoint} for {Symbol}", endpoint.Split('?')[0], symbol);

                var response = await _http.GetAsync(endpoint, ct).ConfigureAwait(false);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync(ct).ConfigureAwait(false);
                    var nextEarnings = await ParseEarningsResponseAsync(content, symbol);

                    if (nextEarnings.HasValue)
                    {
                        _logger.LogInformation("✅ Found earnings data for {Symbol} using endpoint {Endpoint}: {Date}",
                            symbol, endpoint.Split('?')[0], nextEarnings.Value);

                        // Cache the successful result
                        _cache.Set(cacheKey, nextEarnings, CacheDuration);
                        return nextEarnings;
                    }
                }
                else
                {
                    _logger.LogDebug("Endpoint {Endpoint} returned {StatusCode} for {Symbol}",
                        endpoint.Split('?')[0], response.StatusCode, symbol);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error with endpoint {Endpoint} for {Symbol}", endpoint.Split('?')[0], symbol);
            }
        }

        _logger.LogWarning("❌ All Polygon earnings endpoints failed for {Symbol}. " +
                          "This may indicate: 1) Symbol not found, 2) No earnings data available, " +
                          "3) API subscription doesn't include earnings data, or 4) Endpoint has changed", symbol);

        // Cache null result for shorter duration to avoid repeated API calls
        _cache.Set(cacheKey, (DateTime?)null, TimeSpan.FromHours(1));
        return null;
    }

    private Task<DateTime?> ParseEarningsResponseAsync(string content, string symbol)
    {
        try
        {
            var root = JsonSerializer.Deserialize<JsonElement>(content);

            // Try different response structures
            JsonElement resultsArray;

            // Structure 1: { "results": [...] }
            if (root.TryGetProperty("results", out resultsArray) && resultsArray.ValueKind == JsonValueKind.Array)
            {
                return Task.FromResult(ParseEarningsFromResults(resultsArray, symbol));
            }

            // Structure 2: { "earnings": [...] }
            if (root.TryGetProperty("earnings", out resultsArray) && resultsArray.ValueKind == JsonValueKind.Array)
            {
                return Task.FromResult(ParseEarningsFromResults(resultsArray, symbol));
            }

            // Structure 3: Direct array
            if (root.ValueKind == JsonValueKind.Array)
            {
                return Task.FromResult(ParseEarningsFromResults(root, symbol));
            }

            _logger.LogDebug("Unrecognized earnings response structure for {Symbol}", symbol);
            return Task.FromResult<DateTime?>(null);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON parsing error for earnings data for {Symbol}", symbol);
            return Task.FromResult<DateTime?>(null);
        }
    }

    private DateTime? ParseEarningsFromResults(JsonElement resultsArray, string symbol)
    {
        if (resultsArray.GetArrayLength() == 0)
        {
            _logger.LogDebug("No earnings data found in results for {Symbol}", symbol);
            return null;
        }

        var now = DateTime.UtcNow;
        DateTime? nextEarnings = null;

        foreach (var result in resultsArray.EnumerateArray())
        {
            // Try different field names for earnings date
            var dateFields = new[] { "reportTime", "report_time", "date", "earnings_date", "announcement_date" };

            foreach (var field in dateFields)
            {
                if (result.TryGetProperty(field, out var dateElement))
                {
                    var dateStr = dateElement.GetString();
                    if (!string.IsNullOrEmpty(dateStr) && DateTime.TryParse(dateStr, out var reportTime))
                    {
                        // Convert to UTC if needed
                        if (reportTime.Kind == DateTimeKind.Unspecified)
                        {
                            reportTime = DateTime.SpecifyKind(reportTime, DateTimeKind.Utc);
                        }
                        else if (reportTime.Kind == DateTimeKind.Local)
                        {
                            reportTime = reportTime.ToUniversalTime();
                        }

                        // Only consider future earnings
                        if (reportTime > now && (nextEarnings == null || reportTime < nextEarnings))
                        {
                            nextEarnings = reportTime;
                            _logger.LogDebug("Found future earnings date for {Symbol}: {Date} from field {Field}",
                                symbol, reportTime, field);
                        }
                        break; // Found a valid date, move to next result
                    }
                }
            }
        }

        return nextEarnings;
    }
}
